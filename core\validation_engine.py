"""
Main validation engine that orchestrates all validation operations
"""

from typing import Dict, Any, List
from .enums import ValidationType
from .constants import ValidationConstants
from validators.regex_validator import RegexValidator
from validators.expression_validator import ExpressionValidator


class ValidationEngine:
    
    def __init__(self):
        self.regex_validator = RegexValidator()
        self.expression_validator = ExpressionValidator()
    
    def validate_field(self, field_value: Any, field_config: Dict, config: Dict, 
                      all_data: Dict = None, current_group: str = None, 
                      current_field: str = None) -> List[str]:
        field_errors = []
        
        if field_value == "N/A":
            field_value = ""
        
        if field_config.get("required", False) and (field_value is None or field_value == ""):
            field_errors.append(ValidationConstants.ERROR_MESSAGES["REQUIRED_FIELD"])
        
        if not field_config.get("required", False) and (field_value is None or field_value == ""):
            return field_errors
        
        validation_rules = field_config.get("validation_rules", [])
        for rule in validation_rules:
            validation_type = rule.get("isValidationType")
            
            try:
                validation_enum = ValidationType(validation_type)
            except ValueError:
                supported_types = [vt.value for vt in ValidationType]
                error_msg = ValidationConstants.ERROR_MESSAGES["UNSUPPORTED_VALIDATION_TYPE"].format(
                    type=validation_type, 
                    supported_types=supported_types
                )
                field_errors.append(error_msg)
                continue
            
            is_valid, error_msg = self._route_validation(
                validation_enum, field_value, rule, config, all_data, current_group, current_field
            )
            
            if not is_valid:
                field_errors.append(error_msg)
                break  
        
        return field_errors
    
    def _route_validation(self, validation_type: ValidationType, field_value: Any, 
                         rule: Dict, config: Dict, all_data: Dict, 
                         current_group: str, current_field: str) -> tuple:
        
        match validation_type:
            case ValidationType.REGEX:
                return self.regex_validator.validate_regex(field_value, rule)
            
            case ValidationType.REGEX_LIST:
                return self.regex_validator.validate_regex_list(field_value, rule)
            
            case ValidationType.EXPRESSION_TYPE:
                return self.expression_validator.validate_expression(
                    field_value, rule, config, all_data, current_group, current_field
                )
            
            case ValidationType.EXPRESSION_TYPE_LIST:
                return self.expression_validator.validate_expression_list(
                    field_value, rule, config, all_data, current_group, current_field
                )
            
            case _:
                return False, f"Unsupported validation type: {validation_type.value}"

    def validate_group_rules(self, config: Dict, all_data: Dict = None) -> List[str]:
        """
        Validate group-level validation rules

        Args:
            config: Global configuration containing group validation rules
            all_data: All data for cross-field validation

        Returns:
            List of group-level validation errors
        """
        group_errors = []

        # Get group validation rules from config
        groups_config = config.get("properties", {}).get("groups", {})
        group_validation_rules = groups_config.get("title_validation_rules", [])

        if not group_validation_rules:
            return group_errors

        for rule in group_validation_rules:
            validation_type = rule.get("isValidationType")

            try:
                validation_enum = ValidationType(validation_type)
            except ValueError:
                supported_types = [vt.value for vt in ValidationType]
                error_msg = ValidationConstants.ERROR_MESSAGES["UNSUPPORTED_VALIDATION_TYPE"].format(
                    type=validation_type,
                    supported_types=supported_types
                )
                group_errors.append(error_msg)
                continue

            # For group-level validation, we pass None as field_value since we're validating across groups
            is_valid, error_msg = self._route_validation(
                validation_enum, None, rule, config, all_data, None, None
            )

            if not is_valid:
                group_errors.append(error_msg)

        return group_errors

    def _flatten_group_data(self, all_data: Dict) -> Dict:
        """
        Flatten group data structure for easier field access in expressions

        Args:
            all_data: All data containing groups and fields

        Returns:
            Flattened dictionary with group.field.value structure
        """
        flattened = {}

        if not all_data or "groups" not in all_data:
            return flattened

        groups = all_data["groups"]

        for group_name, group_data in groups.items():
            fields = group_data.get("fields", {})

            for field_name, field_data in fields.items():
                # Handle both dict format (with value key) and direct value format
                if isinstance(field_data, dict) and "value" in field_data:
                    field_value = field_data["value"]
                else:
                    field_value = field_data

                # Create flattened key: group.field.value
                flattened_key = f"{group_name}.{field_name}.value"
                flattened[flattened_key] = field_value

                # Also create group.field for backward compatibility
                field_key = f"{group_name}.{field_name}"
                flattened[field_key] = {"value": field_value}

        return flattened
