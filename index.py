# -*- coding: utf-8 -*-
import json
from typing import Dict, <PERSON><PERSON>
from db_utils import DatabaseClient
from core.validation_engine import ValidationEngine


class WorksheetValidator:
    """Main validator class that orchestrates worksheet validation using the new ValidationEngine"""

    def __init__(self, db_client: DatabaseClient = None):
        self.config_data = None
        self.db_client = db_client or DatabaseClient()
        self.validation_engine = ValidationEngine()


    def validate_worksheet_data(self, data: dict, config_key: str, config: Dict) -> Tuple[bool, dict]:
        """Validate worksheet data against configuration rules"""
        config_data = self._extract_validation_config(config_key, config)
        if not config_data:
            return False, {"error": f"Configuration not found for key: {config_key}"}

        updated_data = data.copy()
        all_valid = True

        groups = data.get("groups", {})

        for group_name, group_data in groups.items():
            if "bre_exceptions" not in updated_data["groups"][group_name]:
                updated_data["groups"][group_name]["bre_exceptions"] = {}

            fields = group_data.get("fields", {})

            for field_name, field_data in fields.items():
                field_value = field_data.get("value") if isinstance(field_data, dict) else field_data

                field_config = self._find_field_config(field_name, config_data)
                if not field_config:
                    continue

                field_errors = self.validation_engine.validate_field(
                    field_value, field_config, config_data, data, group_name, field_name
                )

                if field_errors:
                    all_valid = False
                    updated_data["groups"][group_name]["bre_exceptions"][field_name] = "; ".join(field_errors)

        # Perform group-level validation - pass the full config with properties
        full_config = {"properties": config_data}
        group_errors = self.validation_engine.validate_group_rules(full_config, data)

        if group_errors:
            all_valid = False
            # Add group-level errors to a special section
            if "group_level_exceptions" not in updated_data:
                updated_data["group_level_exceptions"] = {}

            for i, error in enumerate(group_errors):
                updated_data["group_level_exceptions"][f"group_rule_{i+1}"] = error

        return all_valid, updated_data

    def _extract_validation_config(self, config_key: str, config: Dict) -> Dict:
        """Extract validation configuration for the given key"""
        # If config has 'properties' key, use that as the validation config
        if 'properties' in config:
            return config['properties']
        # Otherwise, try to get the config_key from the config
        return config.get(config_key, config)

    def _find_field_config(self, field_name: str, config_data: Dict) -> Dict:
        """Find field configuration in the config data"""
        fields_config = config_data.get("fields", {})
        return fields_config.get(field_name, {})

def example_worksheet_usage():
    """Example usage of the WorksheetValidator"""
    try:
        # Load worksheet data
        try:
            with open("test_workSheet.json", "r") as f:
                worksheet_data = json.load(f)
        except FileNotFoundError:
            print("❌ test_workSheet.json file not found")
            return None
        except json.JSONDecodeError:
            print("❌ Invalid JSON in test_workSheet.json")
            return None

        client = DatabaseClient()
        validator = WorksheetValidator(client)

        # Load config from database
        key = "tag_titles"
        config_data = client.get_config(key)
        if not config_data:
            print(f"❌ Configuration not found in database for key: {key}")
            return None

        print(f"✅ Configuration loaded from database for key: {key}")
        print(f"📊 Config structure: {list(config_data.keys())}")

        is_valid, updated_data = validator.validate_worksheet_data(worksheet_data, key, config_data)

        if is_valid:
            print("✅ Worksheet validation passed!")
        else:
            print("❌ Worksheet validation failed. Check bre_exceptions in groups:")

            total_exceptions = 0
            for group_name, group_data in updated_data.get("groups", {}).items():
                exceptions = group_data.get("bre_exceptions", {})
                if exceptions:
                    total_exceptions += len(exceptions)
                    print(f"\n  📋 Group '{group_name}' has {len(exceptions)} exception(s):")
                    for field, error in exceptions.items():
                        print(f"    ❌ {field}: {error}")
                else:
                    print(f"\n  ✅ Group '{group_name}': No exceptions")

            # Display group-level exceptions
            group_level_exceptions = updated_data.get("group_level_exceptions", {})
            if group_level_exceptions:
                print(f"\n  🔍 Group-level validation has {len(group_level_exceptions)} exception(s):")
                for rule_name, error in group_level_exceptions.items():
                    print(f"    ❌ {rule_name}: {error}")
                total_exceptions += len(group_level_exceptions)

            if total_exceptions == 0:
                print("\n  ℹ️  No specific field exceptions found, but validation failed.")
                print("     This might indicate a configuration or data structure issue.")

        with open("validated_output.json", "w") as f:
            json.dump(updated_data, f, indent=2)
        print(f"\n💾 Updated data with bre_exceptions saved to 'validated_output.json'")

        return updated_data

    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return None


if __name__ == "__main__":
    example_worksheet_usage()