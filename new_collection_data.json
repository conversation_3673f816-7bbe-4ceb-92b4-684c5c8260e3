{"validation_type_key": "tag_titles", "properties": [{"validation_rules": [{"id": "title_presence_validation", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.title_received.value == True or bos.title_received.value == \"true\" or bos.title_received.value == 1"], "error_msg": ["Title Not Yet Received: Deal assigned for processing before title or MSO is physically or digitally available"], "conditionType": "AND", "groups": []}]}, {"validation_rules": [{"id": "reassignment_mv7d_validation", "ValidationType": "EXPRESSION_TYPE", "expression": ["not (bos.vehicle_type.value == \"used\" and bos.title_fully_assigned.value == True) or (red_reassignment.vin.value != None and red_reassignment.vin.value != \"\")"], "error_msg": ["Reassignment Needed but Not Included: Used vehicle with title fully reassigned, but no MV-7D included"], "conditionType": "AND", "groups": []}]}, {"validation_rules": [{"id": "title_chain_sequence_validation", "ValidationType": "EXPRESSION_TYPE", "expression": ["(title.selling_dealer_name.value != None and title.selling_dealer_name.value != \"\" and title.selling_dealer_name.value != \"N/A\") and (title.buyer_name.value == red_reassignment.buyer_name.value) and (red_reassignment.buyer_name.value == bos.buyer_name.value) and (bos.buyer_name.value == mvl.buyer_full_name.value)"], "error_msg": ["Title Chain Broken or Not Assigned: Missing reassignment between owner and dealership - buyer names must flow correctly through title → reassignment → sale → registration"], "conditionType": "AND", "groups": []}]}, {"validation_rules": [{"id": "incomplete_reassignment_validation", "ValidationType": "EXPRESSION_TYPE", "expression": ["(red_reassignment.vin.value != None and red_reassignment.vin.value != \"\") and (red_reassignment.odometer_reading.value != None and red_reassignment.odometer_reading.value != \"\") and (red_reassignment.date_of_reassignment.value != None and red_reassignment.date_of_reassignment.value != \"\") and (red_reassignment.buyer_name.value != None and red_reassignment.buyer_name.value != \"\")"], "error_msg": ["Incomplete Reassignment: Reassignment form missing required signatures, VIN, mileage, or transfer date"], "conditionType": "AND", "groups": []}]}, {"validation_rules": [{"id": "bos.deal_number.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9]+$"], "error_msgs": ["Deal number is required", "Deal number should be alphanumeric"], "conditionType": "AND", "groups": ["bos"]}]}, {"validation_rules": [{"id": "mvl.deal_number.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9]+$"], "error_msgs": ["Deal number is required", "Deal number should be alphanumeric"], "conditionType": "AND", "groups": ["mvl"]}]}, {"validation_rules": [{"id": "bos.stock_number.value", "ValidationType": "REGEX", "regex": "^[A-Za-z0-9-]{1,20}$", "error_msg": "Stock number is required and should be alphanumeric with hyphens (1-20 characters)", "groups": ["bos"]}]}, {"validation_rules": [{"id": "mvl.stock_number.value", "ValidationType": "REGEX", "regex": "^[A-Za-z0-9-]{1,20}$", "error_msg": "Stock number is required and should be alphanumeric with hyphens (1-20 characters)", "groups": ["mvl"]}]}, {"validation_rules": [{"id": "bos.vin.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-HJ-NPR-Z0-9]{17}$"], "error_msgs": ["VIN is required", "VIN should be exactly 17 characters (excluding I, O, Q)"], "conditionType": "AND", "groups": ["bos"]}, {"id": "bos.vin.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.vin.value == mvl.vin.value and mvl.vin.value == title.vin.value and title.vin.value == red_reassignment.vin.value or bos.vin.value == mv7d.vin.value and mvl.vin.value == title.vin.value and title.vin.value == mv7d.vin.value"], "error_msg": ["VIN number does not match across documents"], "conditionType": "AND", "groups": ["bos"]}]}, {"validation_rules": [{"id": "mvl.vin.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-HJ-NPR-Z0-9]{17}$"], "error_msgs": ["VIN is required", "VIN should be exactly 17 characters (excluding I, O, Q)"], "conditionType": "AND", "groups": ["mvl"]}, {"id": "mvl.vin.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.vin.value == mvl.vin.value and mvl.vin.value == title.vin.value and title.vin.value == red_reassignment.vin.value or bos.vin.value == mv7d.vin.value and mvl.vin.value == title.vin.value and title.vin.value == mv7d.vin.value"], "error_msg": ["VIN number does not match across documents"], "conditionType": "AND", "groups": ["mvl"]}]}, {"validation_rules": [{"id": "title.vin.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-HJ-NPR-Z0-9]{17}$"], "error_msgs": ["VIN is required", "VIN should be exactly 17 characters (excluding I, O, Q)"], "conditionType": "AND", "groups": ["title"]}, {"id": "title.vin.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.vin.value == mvl.vin.value and mvl.vin.value == title.vin.value and title.vin.value == red_reassignment.vin.value or bos.vin.value == mv7d.vin.value and mvl.vin.value == title.vin.value and title.vin.value == mv7d.vin.value"], "error_msg": ["VIN number does not match across documents"], "conditionType": "AND", "groups": ["title"]}]}, {"validation_rules": [{"id": "red_reassignment.vin.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-HJ-NPR-Z0-9]{17}$"], "error_msgs": ["VIN is required", "VIN should be exactly 17 characters (excluding I, O, Q)"], "conditionType": "AND", "groups": ["red_reassignment"]}, {"id": "red_reassignment.vin.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.vin.value == mvl.vin.value and mvl.vin.value == title.vin.value and title.vin.value == red_reassignment.vin.value or bos.vin.value == mv7d.vin.value and mvl.vin.value == title.vin.value and title.vin.value == mv7d.vin.value"], "error_msg": ["VIN number does not match across documents"], "conditionType": "AND", "groups": ["red_reassignment"]}]}, {"validation_rules": [{"id": "mv7d.vin.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-HJ-NPR-Z0-9]{17}$"], "error_msgs": ["VIN is required", "VIN should be exactly 17 characters (excluding I, O, Q)"], "conditionType": "AND", "groups": ["mv7d"]}, {"id": "mv7d.vin.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.vin.value == mvl.vin.value and mvl.vin.value == title.vin.value and title.vin.value == red_reassignment.vin.value or bos.vin.value == mv7d.vin.value and mvl.vin.value == title.vin.value and title.vin.value == mv7d.vin.value"], "error_msg": ["VIN number does not match across documents"], "conditionType": "AND", "groups": ["mv7d"]}]}, {"validation_rules": [{"id": "bos.year.value", "ValidationType": "REGEX", "regex": "^(19[0-9]{2}|20[0-2][0-9]|2030)$", "error_msg": "Year is required and should be between 1900 and 2030", "groups": ["bos"]}, {"id": "bos.year.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.year.value == mvl.year.value and mvl.year.value == title.year.value and title.year.value == red_reassignment.year.value or bos.year.value == mvl.year.value and mvl.year.value == title.year.value and title.year.value == mv7d.year.value"], "error_msg": ["Year does not match across documents"], "conditionType": "AND", "groups": ["bos"]}]}, {"validation_rules": [{"id": "mvl.year.value", "ValidationType": "REGEX", "regex": "^(19[0-9]{2}|20[0-2][0-9]|2030)$", "error_msg": "Year is required and should be between 1900 and 2030", "groups": ["mvl"]}, {"id": "mvl.year.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.year.value == mvl.year.value and mvl.year.value == title.year.value and title.year.value == red_reassignment.year.value or bos.year.value == mvl.year.value and mvl.year.value == title.year.value and title.year.value == mv7d.year.value"], "error_msg": ["Year does not match across documents"], "conditionType": "AND", "groups": ["mvl"]}]}, {"validation_rules": [{"id": "title.year.value", "ValidationType": "REGEX", "regex": "^(19[0-9]{2}|20[0-2][0-9]|2030)$", "error_msg": "Year is required and should be between 1900 and 2030", "groups": ["title"]}, {"id": "title.year.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.year.value == mvl.year.value and mvl.year.value == title.year.value and title.year.value == red_reassignment.year.value or bos.year.value == mvl.year.value and mvl.year.value == title.year.value and title.year.value == mv7d.year.value"], "error_msg": ["Year does not match across documents"], "conditionType": "AND", "groups": ["title"]}]}, {"validation_rules": [{"id": "red_reassignment.year.value", "ValidationType": "REGEX", "regex": "^(19[0-9]{2}|20[0-2][0-9]|2030)$", "error_msg": "Year is required and should be between 1900 and 2030", "groups": ["red_reassignment"]}, {"id": "red_reassignment.year.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.year.value == mvl.year.value and mvl.year.value == title.year.value and title.year.value == red_reassignment.year.value or bos.year.value == mvl.year.value and mvl.year.value == title.year.value and title.year.value == mv7d.year.value"], "error_msg": ["Year does not match across documents"], "conditionType": "AND", "groups": ["red_reassignment"]}]}, {"validation_rules": [{"id": "mv7d.year.value", "ValidationType": "REGEX", "regex": "^(19[0-9]{2}|20[0-2][0-9]|2030)$", "error_msg": "Year is required and should be between 1900 and 2030", "groups": ["mv7d"]}, {"id": "mv7d.year.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.year.value == mvl.year.value and mvl.year.value == title.year.value and title.year.value == red_reassignment.year.value or bos.year.value == mvl.year.value and mvl.year.value == title.year.value and title.year.value == mv7d.year.value"], "error_msg": ["Year does not match across documents"], "conditionType": "AND", "groups": ["mv7d"]}]}, {"validation_rules": [{"id": "bos.make.value", "ValidationType": "REGEX", "regex": "^[A-Za-z\\s-]{1,30}$", "error_msg": "Make is required and should contain only letters, spaces, and hyphens (1-30 characters)", "groups": ["bos"]}, {"id": "bos.make.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.make.value == mvl.make.value and mvl.make.value == title.make.value and title.make.value == red_reassignment.make.value or bos.make.value == mvl.make.value and mvl.make.value == title.make.value and title.make.value == mv7d.make.value"], "error_msg": ["Make does not match across documents"], "conditionType": "AND", "groups": ["bos"]}]}, {"validation_rules": [{"id": "mvl.make.value", "ValidationType": "REGEX", "regex": "^[A-Za-z\\s-]{1,30}$", "error_msg": "Make is required and should contain only letters, spaces, and hyphens (1-30 characters)", "groups": ["mvl"]}, {"id": "mvl.make.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.make.value == mvl.make.value and mvl.make.value == title.make.value and title.make.value == red_reassignment.make.value or bos.make.value == mvl.make.value and mvl.make.value == title.make.value and title.make.value == mv7d.make.value"], "error_msg": ["Make does not match across documents"], "conditionType": "AND", "groups": ["mvl"]}]}, {"validation_rules": [{"id": "title.make.value", "ValidationType": "REGEX", "regex": "^[A-Za-z\\s-]{1,30}$", "error_msg": "Make is required and should contain only letters, spaces, and hyphens (1-30 characters)", "groups": ["title"]}, {"id": "title.make.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.make.value == mvl.make.value and mvl.make.value == title.make.value and title.make.value == red_reassignment.make.value or bos.make.value == mvl.make.value and mvl.make.value == title.make.value and title.make.value == mv7d.make.value"], "error_msg": ["Make does not match across documents"], "conditionType": "AND", "groups": ["title"]}]}, {"validation_rules": [{"id": "red_reassignment.make.value", "ValidationType": "REGEX", "regex": "^[A-Za-z\\s-]{1,30}$", "error_msg": "Make is required and should contain only letters, spaces, and hyphens (1-30 characters)", "groups": ["red_reassignment"]}, {"id": "red_reassignment.make.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.make.value == mvl.make.value and mvl.make.value == title.make.value and title.make.value == red_reassignment.make.value or bos.make.value == mvl.make.value and mvl.make.value == title.make.value and title.make.value == mv7d.make.value"], "error_msg": ["Make does not match across documents"], "conditionType": "AND", "groups": ["red_reassignment"]}]}, {"validation_rules": [{"id": "mv7d.make.value", "ValidationType": "REGEX", "regex": "^[A-Za-z\\s-]{1,30}$", "error_msg": "Make is required and should contain only letters, spaces, and hyphens (1-30 characters)", "groups": ["mv7d"]}, {"id": "mv7d.make.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.make.value == mvl.make.value and mvl.make.value == title.make.value and title.make.value == red_reassignment.make.value or bos.make.value == mvl.make.value and mvl.make.value == title.make.value and title.make.value == mv7d.make.value"], "error_msg": ["Make does not match across documents"], "conditionType": "AND", "groups": ["mv7d"]}]}, {"validation_rules": [{"id": "bos.model.value", "ValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s-]{1,30}$", "error_msg": "Model is required and should contain only letters, numbers, spaces, and hyphens (1-30 characters)", "groups": ["bos"]}, {"id": "bos.model.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.model.value == mvl.model.value and mvl.model.value == title.model.value and title.model.value == red_reassignment.model.value or bos.model.value == mvl.model.value and mvl.model.value == title.model.value and title.model.value == mv7d.model.value"], "error_msg": ["Model does not match across documents"], "conditionType": "AND", "groups": ["bos"]}]}, {"validation_rules": [{"id": "mvl.model.value", "ValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s-]{1,30}$", "error_msg": "Model is required and should contain only letters, numbers, spaces, and hyphens (1-30 characters)", "groups": ["mvl"]}, {"id": "mvl.model.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.model.value == mvl.model.value and mvl.model.value == title.model.value and title.model.value == red_reassignment.model.value or bos.model.value == mvl.model.value and mvl.model.value == title.model.value and title.model.value == mv7d.model.value"], "error_msg": ["Model does not match across documents"], "conditionType": "AND", "groups": ["mvl"]}]}, {"validation_rules": [{"id": "bos.odometer_reading.value", "ValidationType": "REGEX", "regex": "^(0|[1-9][0-9]{0,5})$", "error_msg": "Odometer reading should be a positive number between 0 and 999,999", "groups": ["bos"]}, {"id": "bos.odometer_reading.consistency", "ValidationType": "EXPRESSION_TYPE_LIST", "expressions": ["abs(safe_subtract(bos.odometer_reading.value, mvl.odometer_reading.value)) <= 10 and abs(safe_subtract(mvl.odometer_reading.value, red_reassignment.odometer_reading.value)) <= 10 and abs(safe_subtract(red_reassignment.odometer_reading.value, title.odometer_reading.value)) <= 10", "title.odometer_reading.value <= bos.odometer_reading.value"], "error_msgs": ["Odometer reading does not match across documents", "Title odometer may be less than or equal to BOS odometer reading - this is acceptable"], "conditionType": "AND", "groups": ["bos"]}]}, {"validation_rules": [{"id": "mvl.odometer_reading.value", "ValidationType": "REGEX", "regex": "^(0|[1-9][0-9]{0,5})$", "error_msg": "Odometer reading should be a positive number between 0 and 999,999", "groups": ["mvl"]}, {"id": "mvl.odometer_reading.consistency", "ValidationType": "EXPRESSION_TYPE_LIST", "expressions": ["abs(safe_subtract(bos.odometer_reading.value, mvl.odometer_reading.value)) <= 10 and abs(safe_subtract(mvl.odometer_reading.value, red_reassignment.odometer_reading.value)) <= 10 and abs(safe_subtract(red_reassignment.odometer_reading.value, title.odometer_reading.value)) <= 10", "title.odometer_reading.value <= bos.odometer_reading.value"], "error_msgs": ["Odometer reading does not match across documents", "Title odometer may be less than or equal to BOS odometer reading - this is acceptable"], "conditionType": "AND", "groups": ["mvl"]}]}, {"validation_rules": [{"id": "bos.buyer_name.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z\\s'-]{2,50}$"], "error_msgs": ["Buyer name is required", "Buyer name should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"], "conditionType": "AND", "groups": ["bos"]}, {"id": "bos.buyer_name.consistency", "ValidationType": "EXPRESSION_TYPE_LIST", "expressions": ["bos.buyer_name.value == dl.full_name.value and dl.full_name.value == mvl.buyer_full_name.value and mvl.buyer_full_name.value == red_reassignment.buyer_name.value and red_reassignment.buyer_name.value == title.buyer_name.value or bos.buyer_name.value == dl.full_name.value and dl.full_name.value == mvl.buyer_full_name.value and mvl.buyer_full_name.value == mv7d.buyer_name.value and .buyer_name.value == title.co_buyer_name.value or bos.buyer_name.value == dl.full_name.value and dl.full_name.value == mvl.buyer_full_name.value and mvl.buyer_full_name.value"], "error_msgs": ["Buyer name does not match across documents"], "conditionType": "AND", "groups": ["bos"]}]}, {"validation_rules": [{"id": "dl.full_name.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z\\s'-]{2,50}$"], "error_msgs": ["Full name is required", "Full name should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"], "conditionType": "AND", "groups": ["dl"]}, {"id": "dl.full_name.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.buyer_name.value == mvl.buyer_full_name.value"], "error_msg": ["Full name does not match across documents"], "conditionType": "AND", "groups": ["dl"]}]}, {"validation_rules": [{"id": "dl.date_of_birth.value", "ValidationType": "REGEX_LIST", "regexes": ["^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/((19[0-9]{2})|200[0-9])$", "^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/((19[0-9]{2})|200[0-8])$"], "error_msgs": ["Date of birth is required and should be in MM/DD/YYYY format with a valid year between 1900 and 2009", "Date of birth should be in MM/DD/YYYY format and indicate an age between 16 and 120 years"], "conditionType": "AND", "groups": ["dl"]}]}, {"validation_rules": [{"id": "dl.address.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[0-9]+\\s+[A-Za-z0-9\\s,.-]{1,100}$"], "error_msgs": ["address is required", "address should start with a number followed by street name (1-100 characters)"], "conditionType": "AND", "groups": ["dl"]}]}, {"validation_rules": [{"id": "dl.city.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z\\s'-]{2,50}$"], "error_msgs": ["City is required", "City should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"], "conditionType": "AND", "groups": ["dl"]}]}, {"validation_rules": [{"id": "dl.state.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Z]{2}$"], "error_msgs": ["State is required", "State should be a 2-letter abbreviation (e.g., GA, FL)"], "conditionType": "AND", "groups": ["dl"]}]}, {"validation_rules": [{"id": "dl.zip.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[0-9]{5}(-[0-9]{4})?$"], "error_msgs": ["ZIP code is required", "ZIP code should be 5 digits or 5+4 format (e.g., 12345 or 12345-6789)"], "conditionType": "AND", "groups": ["dl"]}]}, {"validation_rules": [{"id": "dl.driver_s_license_number.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9]{6,20}$"], "error_msgs": ["Driver's license number is required", "Driver's license number should be 6-20 alphanumeric characters"], "conditionType": "AND", "groups": ["dl"]}, {"id": "dl.driver_s_license_number.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["dl.driver_s_license_number.value == red_reassignment.customer_id.value or  dl.driver_s_license_number.value == mv7d.customer_id.value"], "error_msg": ["Driver's license number does not match across documents"], "conditionType": "AND", "groups": ["dl"]}]}, {"validation_rules": [{"id": "dl.expiration_date.value", "ValidationType": "REGEX_LIST", "regexes": ["^.+$", "^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/(202[5-9]|20[3-9]\\d|21\\d{2})$"], "error_msgs": ["Driver's license expiration date is required", "Expiration date should be in MM/DD/YYYY format and must not be expired"], "conditionType": "AND", "groups": ["dl"]}, {"id": "dl.expiration_date.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["dl.expiration_date.value >= today()"], "error_msg": ["Expiration date must be in the future"], "conditionType": "AND", "groups": ["dl"]}]}, {"validation_rules": [{"id": "mvl.buyer_full_name.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z\\s'-]{2,50}$"], "error_msgs": ["Buyer full name is required", "Buyer full name should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"], "conditionType": "AND", "groups": ["mvl"]}, {"id": "mvl.buyer_full_name.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.buyer_name.value == mvl.buyer_full_name.value and mvl.buyer_full_name.value == dl.full_name.value"], "error_msg": ["Buyer full name does not match across documents"], "conditionType": "AND", "groups": ["mvl"]}]}, {"validation_rules": [{"id": "mvl.county_of_residence.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z\\s'-]{2,50}$"], "error_msgs": ["County of residence is required", "County should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"], "conditionType": "AND", "groups": ["mvl"]}]}, {"validation_rules": [{"id": "mvl.body_style.value", "ValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s-]{1,20}$", "error_msg": "Body style should contain only letters, numbers, spaces, and hyphens (1-20 characters)", "groups": ["mvl"]}]}, {"validation_rules": [{"id": "mvl.lien_holder_address.value", "ValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Lien holder address is required", "groups": ["mvl"]}]}, {"validation_rules": [{"id": "mvl.dealer_name.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9\\s&.,'-]{2,100}$"], "error_msgs": ["Dealer name is required", "Dealer name should be 2-100 characters with valid business name characters"], "conditionType": "AND", "groups": ["mvl"]}, {"id": "mvl.dealer_name.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["mvl.dealer_name.value == bos.buyer_name.value"], "error_msg": ["Dealer name does not match across documents"], "conditionType": "AND", "groups": ["mvl"]}]}, {"validation_rules": [{"id": "mvl.dealer_number.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9]{3,20}$"], "error_msgs": ["Dealer number is required", "Dealer number should be 3-20 alphanumeric characters"], "conditionType": "AND", "groups": ["mvl"]}]}, {"validation_rules": [{"id": "mvl.tavt_tax_amount.value", "ValidationType": "REGEX_LIST", "regexes": ["^(0(\\.\\d{1,2})?|[1-9]\\d{0,4}(\\.\\d{1,2})?|99999(\\.99?)?)$", "^.+$"], "error_msgs": ["TAVT tax amount is required and should be between $0 and $99,999.99", "TAVT tax amount is required"], "conditionType": "AND", "groups": ["mvl"]}, {"id": "mvl.tavt_tax_amount.consistency", "ValidationType": "EXPRESSION_TYPE_LIST", "expressions": ["abs(safe_subtract(bos.tavt_tax_amount.value, mvl.tavt_tax_amount.value)) <= 500 and abs(safe_subtract(mvl.tavt_tax_amount.value, dealer_dmv.tavt_tax_amount.value)) <= 500", "bos.field.value == mvl.field.value and mvl.field.value == dealer_dmv.field.value"], "error_msgs": ["TAVT tax amount differs by more than $500 across documents - requires manual review", "TAVT calculation does not match expected amount based on sale price and county tax rate"], "conditionType": "AND", "groups": ["mvl"]}]}, {"validation_rules": [{"id": "red_reassignment.customer_id.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9]{6,20}$"], "error_msgs": ["Customer ID (Driver's License Number) is required", "Customer ID should be 6-20 alphanumeric characters"], "conditionType": "AND", "groups": ["red_reassignment"]}, {"id": "red_reassignment.customer_id.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["red_reassignment.customer_id.value == dl.driver_s_license_number.value or  mv7d.customer_id.value == dl.driver_s_license_number.value"], "error_msg": ["Customer ID does not match across documents"], "conditionType": "AND", "groups": ["red_reassignment"]}]}, {"validation_rules": [{"id": "red_reassignment.odometer_type.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^(Actual|Exceeds|Not Actual)$"], "error_msgs": ["Odometer type is required", "Odometer type should be 'Actual', 'Exceeds', or 'Not Actual'"], "conditionType": "AND", "groups": ["red_reassignment"]}]}, {"validation_rules": [{"id": "red_reassignment.date_of_reassignment.value", "ValidationType": "REGEX_LIST", "regexes": ["^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/(200\\d|201\\d|202[0-5])$", "^.+$"], "error_msgs": ["Date of reassignment should be in MM/DD/YYYY format and between 2000 and 2025", "Date of reassignment is required"], "conditionType": "AND", "groups": ["red_reassignment"]}, {"id": "red_reassignment.date_sequence", "ValidationType": "EXPRESSION_TYPE", "expression": ["true"], "error_msg": ["Reassignment date must be before transfer date"], "conditionType": "AND", "groups": ["red_reassignment"]}]}, {"validation_rules": [{"id": "title.selling_dealer_name.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9\\s&.,'-]{2,100}$"], "error_msgs": ["Selling dealer name is required", "Selling dealer name should be 2-100 characters with valid business name characters"], "conditionType": "AND", "groups": ["title"]}, {"id": "title.selling_dealer_name.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["title.selling_dealer_name.value == bos.buyer_name.value or title.selling_dealer_name.value == red_reassignment.buyer_name.value"], "error_msg": ["Selling dealer name does not match across documents"], "conditionType": "AND", "groups": ["title"]}]}, {"validation_rules": [{"id": "title.lien_satisfied.value", "ValidationType": "EXPRESSION_TYPE", "expression": ["value == True or value == False or value == \"true\" or value == \"false\" or value == 1 or value == 0"], "error_msg": ["Lien satisfied should be true or false"], "conditionType": "AND", "groups": ["title"]}]}, {"validation_rules": [{"id": "title.date_of_transfer.value", "ValidationType": "REGEX_LIST", "regexes": ["^.+$", "^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/(200\\d|201\\d|202[0-5])$"], "error_msgs": ["Date of transfer is required", "Date of transfer should be in MM/DD/YYYY format and between 01/01/2000 and current date"], "conditionType": "AND", "groups": ["title"]}]}, {"validation_rules": [{"id": "title.title_number.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9]{6,20}$"], "error_msgs": ["Title number is required", "Title number should be 6-20 alphanumeric characters"], "conditionType": "AND", "groups": ["title"]}]}, {"validation_rules": [{"id": "title.lien_release_section.value", "ValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s&.,'-]{2,200}$", "error_msg": "Lien release section should be 2-200 characters with valid text characters", "groups": ["title"]}]}, {"validation_rules": [{"id": "bos.sale_price.value", "ValidationType": "REGEX", "regex": "^(0\\.(0[1-9]|[1-9][0-9]?)|[1-9][0-9]{0,5}(\\.[0-9]{1,2})?|999999(\\.99?)?)$", "error_msg": "Sale price should be a positive amount between $0.01 and $999,999.99", "groups": ["bos"]}]}, {"validation_rules": [{"id": "bos.trade_in_value.value", "ValidationType": "REGEX", "regex": "^(0(\\.\\d{1,2})?|[1-9]\\d{0,5}(\\.\\d{1,2})?|999999(\\.99?)?)$", "error_msg": "Trade-in value should be a positive amount up to $999,999.99", "groups": ["bos"]}, {"id": "bos.trade_in_value.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["True"], "error_msg": ["Trade-in value does not match between Bill of Sale and Red Reassignment"], "conditionType": "AND", "groups": ["bos"]}]}, {"validation_rules": [{"id": "bos.tavt_tax_amount.value", "ValidationType": "REGEX", "regex": "^(0(\\.\\d{1,2})?|[1-9]\\d{0,4}(\\.\\d{1,2})?|99999(\\.99?)?)$", "error_msg": "TAVT tax amount is required and should be between $0 and $99,999.99", "groups": ["bos"]}]}, {"validation_rules": [{"id": "bos.total_amount_due.value", "ValidationType": "REGEX", "regex": "^(0(\\.\\d{1,2})?|[1-9]\\d{0,5}(\\.\\d{1,2})?|999999(\\.99?)?)$", "error_msg": "Total amount due should be a positive amount up to $999,999.99", "groups": ["bos"]}, {"id": "bos.total_amount_due.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.field.value == red_reassignment.field.value and red_reassignment.field.value == dealer_dmv.field.value"], "error_msg": ["Total amount due does not match across funding and documents"], "conditionType": "AND", "groups": ["bos"]}]}, {"validation_rules": [{"id": "bos.lien_holder_name.value", "ValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s&.,'-]{2,100}$", "error_msg": "Lien holder name should be 2-100 characters with valid business name characters", "groups": ["bos"]}, {"id": "bos.lien_holder_name.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.field.value == mvl.field.value and mvl.field.value == title.field.value"], "error_msg": ["Lien holder name does not match across documents"], "conditionType": "AND", "groups": ["bos"]}]}, {"validation_rules": [{"id": "bos.co_buyer_name.value", "ValidationType": "REGEX", "regex": "^[A-Za-z\\s'-]{2,50}$", "error_msg": "Co-buyer name should contain only letters, spaces, hyphens and apostrophes", "groups": ["bos"]}, {"id": "bos.co_buyer_name.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["bos.co_buyer_name.value == mvl.co_buyer_name.value"], "error_msg": ["Co-buyer name does not match across documents"], "conditionType": "AND", "groups": ["bos"]}]}, {"validation_rules": [{"id": "bos.buyer_address.value", "ValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Buyer address is required", "groups": ["bos"]}, {"id": "bos.buyer_address.consistency", "ValidationType": "EXPRESSION_TYPE_LIST", "expressions": ["True", "buyer_address.value == mvl.lien_holder_address.value"], "error_msgs": ["Buyer address consistency check disabled", "Buyer address does not match lien holder address in MV1"], "conditionType": "AND", "groups": ["bos"]}]}, {"validation_rules": [{"id": "bos.deal_status.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^(funded|accounting|finalized)$"], "error_msgs": ["Deal status is required", "Deal must be fully finalized (funded, in accounting, or finalized) before processing"], "conditionType": "AND", "groups": ["bos"]}]}, {"validation_rules": [{"id": "bos.title_received.value", "ValidationType": "EXPRESSION_TYPE", "expression": ["value == True or value == False or value == \"true\" or value == \"false\" or value == 1 or value == 0"], "error_msg": ["Title or MSO must be physically or digitally available before processing"], "conditionType": "AND", "groups": ["bos"]}]}, {"validation_rules": [{"id": "bos.vehicle_type.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^(new|used)$"], "error_msgs": ["Vehicle type is required", "Vehicle type must be 'new' or 'used'"], "conditionType": "AND", "groups": ["bos"]}]}, {"validation_rules": [{"id": "mv7d.customer_id.value", "ValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9]{6,20}$"], "error_msgs": ["Customer ID (Driver's License Number) is required", "Customer ID should be 6-20 alphanumeric characters"], "conditionType": "AND", "groups": ["mv7d"]}, {"id": "mv7d.customer_id.consistency", "ValidationType": "EXPRESSION_TYPE", "expression": ["red_reassignment.customer_id.value == dl.driver_s_license_number.value or  mv7d.customer_id.value == dl.driver_s_license_number.value"], "error_msg": ["Customer ID does not match across documents"], "conditionType": "AND", "groups": ["mv7d"]}]}, {"validation_rules": [{"id": "mv7d.signatures.value", "ValidationType": "EXPRESSION_TYPE", "expression": ["seller_signature.value != None and seller_signature.value != \"\" and buyer_signature.value != None and buyer_signature.value != \"\""], "error_msg": ["Both seller and buyer signatures are required for MV-7D"], "conditionType": "AND", "groups": ["mv7d"]}]}, {"validation_rules": [{"id": "dealer_dmv.dealer_fees.value", "ValidationType": "REGEX_LIST", "regexes": ["^(0(\\.\\d{1,2})?|[1-9]\\d{0,4}(\\.\\d{1,2})?|99999(\\.99?)?)$", "^.+$"], "error_msgs": ["Dealer fees should be a positive amount between $0 and $99,999.99", "Dealer fees is required"], "conditionType": "AND", "groups": ["dealer_dmv"]}]}, {"validation_rules": [{"id": "red_reassignment.seller_signature.value", "ValidationType": "EXPRESSION_TYPE", "expression": ["value != None and value != \"\" and value != False"], "error_msg": ["Seller signature is required"], "conditionType": "AND", "groups": ["red_reassignment"]}]}]}