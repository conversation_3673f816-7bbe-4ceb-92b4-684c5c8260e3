{ "validation_type_key": "tag_titles",
   "properties": [
    {
        "validation_rules": [
               {
                  "id": "mvl.deal_number.value",
                  "ValidationType": "REGEX_LIST",
                  "regexes": [
                     "^.{1,}$",
                     "^[A-Za-z0-9]+$"
                  ],
                  "error_msgs": [
                     "Deal number is required",
                     "Deal number should be alphanumeric"
                  ],
                  "conditionType": "AND",
                   "groups" : ["mvl"]
               }
            ]
    },{
        "validation_rules": [
               {
                  "id": "mvl.deal_number.value",
                  "ValidationType": "REGEX_LIST",
                  "regexes": [
                     "^.{1,}$",
                     "^[A-Za-z0-9]+$"
                  ],
                  "error_msgs": [
                     "Deal number is required",
                     "Deal number should be alphanumeric"
                  ],
                  "conditionType": "AND",
                   "groups" : ["mvl"]
               }
            ]
    },{
        "validation_rules": [
               {
                  "id": "mvl.vin.value",
                  "ValidationType": "REGEX_LIST",
                  "regexes": [
                     "^.{1,}$",
                     "^[A-Za-z0-9]+$"
                  ],
                  "error_msgs": [
                     "Deal number is required",
                     "Deal number should be alphanumeric"
                  ],
                  "conditionType": "AND",
                   "groups" : ["mvl"]
               },
                {
                  "id": "mvl.vin.value",
                  "ValidationType": "EXPRESSION_TYPE",
                  "expression": ["bos.vin.value == mvl.vin.value and mvl.vin.value == title.vin.value and title.vin.value == red_reassignment.vin.value or bos.vin.value == mv7d.vin.value and mvl.vin.value == title.vin.value and title.vin.value == mv7d.vin.value" ],
                  "error_msg": [ "VIN number does not match across documents"],
                  "conditionType": "AND",
                   "groups" : ["mvl"]
               }

            ]
    },{
        "validation_rules": [
               {
                  "id": "dl.vin.value",
                  "ValidationType": "REGEX_LIST",
                  "regexes": [
                     "^.{1,}$",
                     "^[A-Za-z0-9]+$"
                  ],
                  "error_msgs": [
                     "Deal number is required",
                     "Deal number should be alphanumeric"
                  ],
                  "conditionType": "AND",
                   "groups" : ["dl"]
               },
                {
                  "id": "dl.vin.value",
                  "ValidationType": "EXPRESSION_TYPE",
                  "expression": ["bos.vin.value == mvl.vin.value and mvl.vin.value == title.vin.value and title.vin.value == red_reassignment.vin.value or bos.vin.value == mv7d.vin.value and mvl.vin.value == title.vin.value and title.vin.value == mv7d.vin.value" ],
                  "error_msg": [ "VIN number does not match across documents"],
                  "conditionType": "AND",
                   "groups" : ["dl"]
               }

            ]
    },{
        "validation_rules": [
               {
               "id": "title",
               "isValidationType": "EXPRESSION_TYPE",
               "expression": ["bos.title_received.value == True or bos.title_received.value == \"true\" or bos.title_received.value == 1" ],
               "error_msg": ["Title Not Yet Received: Deal assigned for processing before title or MSO is physically or digitally available" ],
               "conditionType": "AND",
                "groups" : []
            }

   ]
}