
////////////// bill_of_sale

•	The Bill of Sale is a legal document that records the sale transaction between the dealership and the buyer. It serves as proof of purchase and outlines the financial terms, buyer and vehicle information, and any trade-in details.
•	In the Tag & Title process, the Bill of Sale is critical for:
o	Verifying sale price and tax calculations (TAVT)
o	Confirming vehicle ownership
o	Matching buyer details with the MV1 and Driver’s License
o	Validating trade-in and lien information
•	Fields to be extracted and validated:

Section	Field Name	Required	Purpose / Validation Target
Deal Info	Deal Number	Yes	DLR Recall
	Stock Number	Yes	Matches vehicle entry across systems
Vehicle Info	VIN	Yes	Core match field across all documents
	Year / Make / Model	Yes	Must match all docs
	Odometer Reading	Yes	Must match all docs
Buyer Info	Buyer Name	Yes	Must match all docs

	Co-Buyer Name (if present)	Yes	Must match all docs

	Buyer Address	Yes	Must match all docs

Sale & Tax Info	Sale Price	Yes	Used to compute tax (TAVT)
	Trade-In Value	If present	Must match all docs with trade data MV-7D,BOS
	Dealer Fees	Yes	Breakdown of total; may appear in DMV
	TAVT / Tax Amount	Yes	Must match with Dealer DMV
	Total Amount Due	Optional	Match with funding and  MV-7D,BOS,DLR 
Lien Info	Lien Holder Name	If applicable	Must match MV1, DMV and BOS 



/////// driver_license

•	The Driver’s License is used to:
o	Verify the identity of the buyer and co-buyer
o	Confirm the state-provided address (used to calculate county and tax jurisdiction)
o	Ensure accuracy of personal details for title registration
•	Driver’s License are scanned and uploaded into the Reynolds Deal Jacket and later used for matching with the MV1, Dealer DMV and MV-7D data. 
•	Fields to be extracted and validated:

Field Name	Required	Purpose / Validation Target
Full Name	 Yes	Must match MV1, Bill of Sale, Dealer DMV
Date of Birth	 Yes	Used for identity verification
Address	 Yes	Used to confirm county and compare with MV1 and DMV record
City, State, ZIP	 Yes	Further verifies residency and jurisdiction
Driver's License Number	 Yes	Used to confirm identity in title application
Expiration Date	Yes	Must be valid




//// mv-1


•	The MV-1 (Title Application) is the official Georgia state form required for registering and titling a vehicle. It includes buyer, vehicle, transaction, and lien information that must be accurate and consistent with other documents.
•	It is considered the anchor document in the title package and is used to:
o	Register the vehicle with the Georgia MVD
o	Apply for a new title (with or without lien)
•	Fields to be extracted and validated:

Section	Field Name	Required	Purpose / Validation Target
Owner Info	Buyer Full Name	 Yes	Must match DL, Bill of Sale, Dealer DMV
	Co-Buyer Name (if present)	 Yes	Cross-check across title and documents
	Buyer Address (Street, City, ZIP)	 Yes	Used to validate county and address
	County of Residence	 Yes	Must match Dealer DMV and tax jurisdiction
	Customer ID (DL Number)	 Yes	Confirms identity across docs
Vehicle Info	VIN	 Yes	Central matching key across all documents
	Year / Make / Model	 Yes	Validates title against Reassignment and DMV
	Body Style	Optional	Additional validation detail
	Odometer Reading	 Yes	Must match BOS and Red reassigment 
Title/Lien Info	Lien Holder Name	 Yes	Must match Bill of Sale and Dealer DMV
	Lien Holder Address	 Yes	Ensure accuracy in lien placement
Dealer Info	Dealer Name	 Yes	Must match seller identity on BOS
	Dealer Number / License ID	 Yes	Required for GA dealer transactions
	Sales Price	 Yes	Used in tax (TAVT) calculation




/////// mv-7d

•	The MV-7D is Georgia’s motor vehicle reassignment form, commonly called the Red Form due to its red color band. It is used when:
o	A dealership transfers ownership of a used vehicle
o	The title does not have space for additional reassignment
o	A formal paper trail of the seller-to-buyer chain is required
•	In the Tag & Title process, it is especially critical when:
o	The vehicle is used
o	There are multiple transfers before reaching the final buyer
o	Supporting documents must show  VIN, mileage, and buyer/seller names
•	Fields to be extracted and validated:

Section	Field Name	Required	Purpose / Validation Target
Vehicle Info	VIN	Yes	Must match MV1, Title, BOS, Dealer DMV
	Year / Make / Model	Yes	Supports VIN decoding and consistency
	Odometer Reading	Yes	Must match BOS, MV1, Dealer DMV
	Odometer Type (Actual, Exceeds, Not Actual)	Yes	Legal requirement for state title submission
Buyer Info	Buyer Name	Yes	Must match MV1, DL, BOS
	Buyer Address	Yes	Should match address used in MV1 and DMV
Date Fields	Date of Reassignment	Yes	Cross-check with BOS and MV1 sale date
Certification	Signatures (Seller and Buyer) 	Needed for audit/compliance confirmation



////// title-MSO


•	The Title (for used vehicles) or MSO (for new vehicles) is the official ownership document that legally proves who owns the vehicle. It must accompany the registration and tax process and is a non-negotiable requirement for title submission to the Georgia DMV via Dealer DMV.
•	In the Tag & Title process, the Title or MSO is used to:
o	Validate VIN and vehicle information
o	Ensure the vehicle was properly reassigned to the current dealer
o	Support legal chain of ownership for reassignment
•	Fields to be extracted and validated:


Section	Field Name	Required	Purpose / Validation Target
Vehicle Info	VIN	Yes	Must match MV1, BOS, MV-7D, Dealer DMV
	Year / Make / Model	Yes	Verifies consistency with deal documents
	Body Style	Optional	Additional detail for classification
	Odometer Reading	Yes	Must match MV1, Red Form, BOS. 
Can be less than the quantity present in BOS.
	Selling Dealer Name	Yes	Must match reassignment or BOS
Lien Info	Lien Holder Name	If any	Must match MV1 and Dealer DMV (only for MSO) 
	Lien Satisfied / Release of Lien section	If released	Proof of clear title before transfer
Assignment Section	Date of Transfer / Reassignment	Yes	Used for title chain verification 
Must increase
	Buyer Name (if present)	Yes	Must match MV1 and BOS
	Seller Signature	Yes	Legal certification of transfer
Title Number	State-issued title number	Yes	Must be provided for state DMV record creation (if available, if not use print date) 


Red Reassignment

•	The Red Reassigment is Georgia’s motor vehicle reassignment form, commonly called the Red Form due to its red color band. It is used when:
o	A dealership transfers ownership of a used vehicle
o	The title does not have space for additional reassignment
o	A formal paper trail of the seller-to-buyer chain is required
•	In the Tag & Title process, it is especially critical when:
o	The vehicle is used
o	There are multiple transfers before reaching the final buyer
o	Supporting documents must show increasing  VIN, mileage, and buyer/seller names
•	Fields to be extracted and validated:

Section	Field Name	Required	Purpose / Validation Target
Vehicle Info	VIN	Yes	Must match MV1, Title, BOS, Dealer DMV
	Year / Make / Model	Yes	Supports VIN decoding and consistency
	Odometer Reading	Yes	Must match BOS, MV1, Dealer DMV
	Odometer Type (Actual, Exceeds, Not Actual)	Yes	Legal requirement for state title submission
Buyer Info	Buyer Name	Yes	Must match MV1, DL, BOS
	Buyer Address	Yes	Should match address used in MV1 and DMV
Date Fields	Date of Reassignment	Yes	Cross-check with BOS and MV1 sale date
Certification	Signatures (Seller and Buyer)	Yes
	Needed for audit/compliance confirmation




////   common

Field Mismatches Across Documents 
1.	VIN Mismatch: VIN differs between MV1, BOS, Title/MSO, Reassignment, or Dealer DMV.
2.	Odometer Mismatch: Mileage on BOS, Reassignment (MV-7D), Title, and MV1 are inconsistent.
3.	Buyer Name Mismatch: Buyer/co-buyer name does not match between MV1, DL, BOS, and DMV entry.
4.	Title Chain Broken or Not Assigned: Missing reassignment between owner and dealership.
5.	Buyer Address or County Mismatch: Address fields differ between MV1, BOS, DL, and Dealer DMV. County mismatch affects TAVT calculation.
6.	Lien Holder Mismatch: Lien holder on MV1 doesn’t match BOS or Title/MSO.
7.	Tax Amount Mismatch: Dealer DMV TAVT value doesn’t match Bill of Sale or MV1.
Note: Needs to match across all documents, if address is different then you will need the MV-34. If not everything must match to BOS.
Document & Data Issues

8.	Incomplete Reassignment: Reassignment form missing signatures, VIN, mileage, or transfer date.
9.	Invalid or Expired Driver’s License: DL is expired, or information is illegible.
Financial / Regulatory Exceptions
10.	TAVT Difference > $500: Tax calculation is off by more than $500; requires manual review.
Timing & Status Exceptions
11.	Deal Not Fully Finalized: Deal missing one or more eligibility conditions (e.g., not funded, not in accounting ).
12.	Title Not Yet Received: Deal assigned for processing before title or MSO is physically or digitally available.
13.	Reassignment Needed but Not Included: Used vehicle with title fully reassigned, but no MV-7D included.

Field Mismatches Across Documents 
1.	VIN Mismatch: VIN differs between MV1, BOS, Title/MSO, Reassignment, or Dealer DMV.
2.	Odometer Mismatch: Mileage on BOS, Reassignment (MV-7D), Title, and MV1 are inconsistent.
3.	Buyer Name Mismatch: Buyer/co-buyer name does not match between MV1, DL, BOS, and DMV entry.
4.	Title Chain Broken or Not Assigned: Missing reassignment between owner and dealership.
5.	Buyer Address or County Mismatch: Address fields differ between MV1, BOS, DL, and Dealer DMV. County mismatch affects TAVT calculation.
6.	Lien Holder Mismatch: Lien holder on MV1 doesn’t match BOS or Title/MSO.
7.	Tax Amount Mismatch: Dealer DMV TAVT value doesn’t match Bill of Sale or MV1.
Note: Needs to match across all documents, if address is different then you will need the MV-34. If not everything must match to BOS.
Document & Data Issues

8.	Incomplete Reassignment: Reassignment form missing signatures, VIN, mileage, or transfer date.
9.	Invalid or Expired Driver’s License: DL is expired, or information is illegible.
Financial / Regulatory Exceptions
10.	TAVT Difference > $500: Tax calculation is off by more than $500; requires manual review.
Timing & Status Exceptions
11.	Deal Not Fully Finalized: Deal missing one or more eligibility conditions (e.g., not funded, not in accounting ).
12.	Title Not Yet Received: Deal assigned for processing before title or MSO is physically or digitally available.
13.	Reassignment Needed but Not Included: Used vehicle with title fully reassigned, but no MV-7D included.
