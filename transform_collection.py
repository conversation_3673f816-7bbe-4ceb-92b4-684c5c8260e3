#!/usr/bin/env python3
"""
Transform collection_json.json from global fields structure to collection-based structure.
"""

import json
import re
from typing import Dict, List, Any

def extract_document_types_from_expression(expression: str) -> List[str]:
    """Extract document types from validation expressions."""
    # Find patterns like 'bos.field.value', 'mvl.field.value', etc.
    pattern = r'(\w+)\.[\w_]+\.value'
    matches = re.findall(pattern, expression)
    return list(set(matches))

def determine_groups_for_field(field_name: str, validation_rule: Dict[str, Any]) -> List[str]:
    """Determine which groups a validation rule should belong to based on field and content."""
    groups = []
    
    # Check if this is a field that should be grouped by document type
    if 'expression' in validation_rule:
        expression = validation_rule['expression']
        if isinstance(expression, list):
            expression = ' '.join(expression)
        
        doc_types = extract_document_types_from_expression(expression)
        groups.extend(doc_types)
    
    # Add specific groupings based on field names and validation content
    if field_name in ['deal_number', 'stock_number', 'vin', 'year', 'make', 'model', 'odometer_reading']:
        if 'mvl' not in groups:
            groups.append('mvl')
    
    if field_name in ['full_name', 'date_of_birth', 'address', 'city', 'state', 'zip', 'driver_s_license_number', 'expiration_date']:
        if 'dl' not in groups:
            groups.append('dl')
    
    if field_name in ['buyer_name', 'co_buyer_name', 'buyer_address', 'sale_price', 'trade_in_value', 'tavt_tax_amount', 'total_amount_due', 'lien_holder_name']:
        if 'bos' not in groups:
            groups.append('bos')
    
    if field_name in ['selling_dealer_name', 'lien_satisfied', 'date_of_transfer', 'title_number', 'lien_release_section']:
        if 'title' not in groups:
            groups.append('title')
    
    if field_name in ['customer_id', 'date_of_reassignment', 'odometer_type', 'signatures']:
        if 'red_reassignment' not in groups:
            groups.append('red_reassignment')
        if 'mv7d' not in groups:
            groups.append('mv7d')
    
    # Remove duplicates and return
    return list(set(groups)) if groups else []

def create_validation_rule_id(field_name: str, doc_type: str) -> str:
    """Create a validation rule ID in the format doc_type.field_name.value"""
    return f"{doc_type}.{field_name}.value"

def transform_validation_rule(field_name: str, rule: Dict[str, Any], doc_type: str) -> Dict[str, Any]:
    """Transform a single validation rule to the new format."""
    new_rule = {
        "id": create_validation_rule_id(field_name, doc_type),
        "ValidationType": rule.get("isValidationType", "REGEX"),
        "groups": determine_groups_for_field(field_name, rule)
    }
    
    # Handle different validation types
    if "regexes" in rule:
        new_rule["ValidationType"] = "REGEX_LIST"
        new_rule["regexes"] = rule["regexes"]
        new_rule["error_msgs"] = rule.get("error_msgs", [])
        if "conditionType" in rule:
            new_rule["conditionType"] = rule["conditionType"]
    elif "regex" in rule:
        new_rule["ValidationType"] = "REGEX"
        new_rule["regex"] = rule["regex"]
        new_rule["error_msg"] = rule.get("error_msg", "")
    elif "expression" in rule:
        new_rule["ValidationType"] = "EXPRESSION_TYPE"
        if isinstance(rule["expression"], list):
            new_rule["expression"] = rule["expression"]
        else:
            new_rule["expression"] = [rule["expression"]]
        
        if "error_msg" in rule:
            new_rule["error_msg"] = [rule["error_msg"]]
        elif "error_msgs" in rule:
            new_rule["error_msg"] = rule["error_msgs"]
        
        if "conditionType" in rule:
            new_rule["conditionType"] = rule["conditionType"]
    elif "expressions" in rule:
        new_rule["ValidationType"] = "EXPRESSION_TYPE_LIST"
        new_rule["expressions"] = rule["expressions"]
        new_rule["error_msgs"] = rule.get("error_msgs", [])
        if "conditionType" in rule:
            new_rule["conditionType"] = rule["conditionType"]
    
    # Ensure groups is always present
    if not new_rule["groups"]:
        new_rule["groups"] = [doc_type] if doc_type else []
    
    return new_rule

def transform_collection_data(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """Transform the entire collection data structure."""
    
    # Start with the basic structure
    output_data = {
        "validation_type_key": input_data.get("validation_type_key", "tag_titles"),
        "properties": []
    }
    
    # First, handle the group-based validation rules (like title validation)
    if "properties" in input_data and "groups" in input_data["properties"]:
        for group_name, group_rules in input_data["properties"]["groups"].items():
            for rule in group_rules:
                property_entry = {
                    "validation_rules": [
                        {
                            "id": rule.get("check", "title"),
                            "isValidationType": rule.get("isValidationType", "EXPRESSION_TYPE"),
                            "expression": [rule.get("expression", "")],
                            "error_msg": [rule.get("error_msg", "")],
                            "conditionType": "AND",
                            "groups": []
                        }
                    ]
                }
                output_data["properties"].append(property_entry)
    
    # Then handle field-based validation rules
    if "properties" in input_data and "fields" in input_data["properties"]:
        # Group fields by document types they reference
        doc_type_groups = {}
        
        for field_name, field_config in input_data["properties"]["fields"].items():
            if "validation_rules" in field_config:
                for rule in field_config["validation_rules"]:
                    # Determine which document types this rule applies to
                    doc_types = determine_groups_for_field(field_name, rule)
                    
                    if not doc_types:
                        # If no specific document types, create a general rule
                        doc_types = ["general"]
                    
                    for doc_type in doc_types:
                        if doc_type not in doc_type_groups:
                            doc_type_groups[doc_type] = []
                        
                        transformed_rule = transform_validation_rule(field_name, rule, doc_type)
                        doc_type_groups[doc_type].append(transformed_rule)
        
        # Create property entries for each document type group
        for doc_type, rules in doc_type_groups.items():
            property_entry = {
                "validation_rules": rules
            }
            output_data["properties"].append(property_entry)
    
    return output_data

def main():
    """Main transformation function."""
    try:
        # Read the input file
        with open('collection_json.json', 'r') as f:
            input_data = json.load(f)
        
        # Transform the data
        output_data = transform_collection_data(input_data)
        
        # Write the output file
        with open('collection_json_new.json', 'w') as f:
            json.dump(output_data, f, indent=3)
        
        print("Transformation completed successfully!")
        print(f"Created collection_json_new.json with {len(output_data['properties'])} property groups")
        
    except Exception as e:
        print(f"Error during transformation: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
